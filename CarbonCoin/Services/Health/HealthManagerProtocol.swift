//
//  HealthManagerProtocol.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/19.
//

import Foundation
import HealthKit

// MARK: - 健康数据类型枚举
enum HealthDataType: String, CaseIterable, Codable {
    case steps = "stepCount"
    case calories = "activeEnergyBurned"
    case heartRate = "heartRate"
    case cyclingTime = "appleExerciseTime"
    case distance = "distanceWalkingRunning"
    
    var displayName: String {
        switch self {
        case .steps: return "步数"
        case .calories: return "卡路里"
        case .heartRate: return "心率"
        case .cyclingTime: return "骑行时间"
        case .distance: return "距离"
        }
    }
    
    var unit: HKUnit {
        switch self {
        case .steps: return .count()
        case .calories: return .kilocalorie()
        case .heartRate: return HKUnit.count().unitDivided(by: .minute())
        case .cyclingTime: return .minute()
        case .distance: return .meterUnit(with: .kilo)
        }
    }
    
    var quantityType: HKQuantityType? {
        switch self {
        case .steps:
            return HKQuantityType.quantityType(forIdentifier: .stepCount)
        case .calories:
            return HKQuantityType.quantityType(forIdentifier: .activeEnergyBurned)
        case .heartRate:
            return HKQuantityType.quantityType(forIdentifier: .heartRate)
        case .cyclingTime:
            return HKQuantityType.quantityType(forIdentifier: .appleExerciseTime)
        case .distance:
            return HKQuantityType.quantityType(forIdentifier: .distanceWalkingRunning)
        }
    }
}

// MARK: - 时间段枚举扩展
extension TimePeriod {
    /// 获取当前时间段的开始时间
    var currentPeriodStartDate: Date {
        let calendar = Calendar.current
        let now = Date()
        
        switch self {
        case .day:
            return calendar.startOfDay(for: now)
        case .week:
            return calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
        case .month:
            return calendar.dateInterval(of: .month, for: now)?.start ?? now
        case .sixMonths:
            return calendar.date(byAdding: .month, value: -6, to: now) ?? now
        }
    }
    
    /// 获取数据聚合间隔
    var aggregationInterval: Calendar.Component {
        switch self {
        case .day: return .hour
        case .week, .month: return .day
        case .sixMonths: return .month
        }
    }
}

// MARK: - 健康数据结构
struct HealthData: Identifiable, Equatable, Codable {
    var id = UUID()
    let date: Date
    let value: Double
    let type: HealthDataType
    
    /// 转换为 StepData（保持兼容性）
    func toStepData() -> StepData {
        let steps = type == .steps ? Int(value) : 0
        let calories = type == .calories ? value : (type == .steps ? value * 0.04 : 0)
        print("Converting HealthData to StepData: date=\(date), steps=\(steps), calories=\(calories)")
        return StepData(date: date, steps: steps, calories: calories)
    }
    
    init(date: Date, value: Double, type: HealthDataType){
        self.date = date
        self.value = value
        self.type = type
    }
}

// MARK: - HealthManager 协议
protocol HealthManagerProtocol {
    /// 请求 HealthKit 授权
    func requestAuthorization(for types: [HealthDataType]) async throws
    
    /// 获取指定类型和时间段的健康数据
    func fetchHealthData(
        type: HealthDataType,
        period: TimePeriod
    ) async throws -> [HealthData]
    
    /// 检查 HealthKit 是否可用
    var isHealthKitAvailable: Bool { get }
}
