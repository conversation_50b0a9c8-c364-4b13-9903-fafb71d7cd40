//
//  Theme.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

// MARK: - Theme Configuration
struct Theme {
    
    // MARK: - Spacing
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 12
        static let lg: CGFloat = 24
        static let xl: CGFloat = 32
        static let xxl: CGFloat = 48
    }
    
    // MARK: - Corner Radius
    struct CornerRadius {
        static let sm: CGFloat = 8
        static let md: CGFloat = 12
        static let lg: CGFloat = 16
        static let xl: CGFloat = 20
        static let xxl: CGFloat = 24
        static let round: CGFloat = 50 // 用于圆形按钮
    }
    
    // MARK: - Font Sizes
    struct FontSize {
        static let caption: CGFloat = 12
        static let body: CGFloat = 16
        static let title3: CGFloat = 20
        static let title2: CGFloat = 24
        static let title1: CGFloat = 28
        static let largeTitle: CGFloat = 34
    }
    
    // MARK: - Icon Sizes
    struct IconSize {
        static let sm: CGFloat = 16
        static let md: CGFloat = 24
        static let lg: CGFloat = 32
        static let xl: CGFloat = 40
        static let xxl: CGFloat = 48
    }
    
    // MARK: - Animation Durations
    struct AnimationDuration {
        static let fast: Double = 0.2
        static let normal: Double = 0.3
        static let slow: Double = 0.5
    }

    struct AnimationStyle {
        static let fast = SwiftUI.Animation.easeInOut(duration: AnimationDuration.fast)
        static let normal = SwiftUI.Animation.easeInOut(duration: AnimationDuration.normal)
        static let slow = SwiftUI.Animation.easeInOut(duration: AnimationDuration.slow)
        static let bouncy = SwiftUI.Animation.interpolatingSpring(stiffness: 300, damping: 28)
        static let easeInOut = SwiftUI.Animation.easeInOut(duration: AnimationDuration.normal)
    }
    
    
    
    // MARK: - Tab Bar Configuration
    struct TabBar {
        static let height: CGFloat = 80
        static let iconSize: CGFloat = 24
        static let selectedIconSize: CGFloat = 28
        static let cornerRadius: CGFloat = 40
        static let horizontalPadding: CGFloat = 4
        static let verticalPadding: CGFloat = 12
        static let backgroundColor = Color.black.opacity(0.8)
        static let selectedBackgroundColor = Color.primaryGradient
    }
}

// MARK: - Custom Font Extensions
extension Font {
    
    // MARK: - Brand Fonts
    static let captionBrand = Font.system(size: Theme.FontSize.caption, weight: .medium, design: .rounded)
    static let bodyBrand = Font.system(size: Theme.FontSize.body, weight: .medium, design: .rounded)
    static let title3Brand = Font.system(size: Theme.FontSize.title3, weight: .semibold, design: .rounded)
    static let title2Brand = Font.system(size: Theme.FontSize.title2, weight: .bold, design: .rounded)
    static let title1Brand = Font.system(size: Theme.FontSize.title1, weight: .bold, design: .rounded)
    static let largeTitleBrand = Font.system(size: Theme.FontSize.largeTitle, weight: .heavy, design: .rounded)
}

// MARK: - Custom Button Styles
struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.bodyBrand)
            .foregroundColor(.textPrimary)
            .padding(.horizontal, Theme.Spacing.lg)
            .padding(.vertical, Theme.Spacing.md)
            .background(Color.primaryGradient)
            .cornerRadius(Theme.CornerRadius.lg)
            .primaryShadow()
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(Theme.AnimationStyle.bouncy, value: configuration.isPressed)
    }
}

struct SecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.bodyBrand)
            .foregroundColor(.textPrimary)
            .padding(.horizontal, Theme.Spacing.lg)
            .padding(.vertical, Theme.Spacing.md)
            .background(Color.defaultButtonBackground)
            .cornerRadius(Theme.CornerRadius.lg)
            .cardShadow()
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(Theme.AnimationStyle.bouncy, value: configuration.isPressed)
    }
}

// MARK: - Glass Card Style
struct GlassCardStyle: ViewModifier {
    func body(content: Content) -> some View {
        content
            .background(
                RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                    .fill(Color.cardBackground)
                    .background(
                        RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                            .stroke(Color.cardBorderGradient, lineWidth: 1)
                    )
            )
            .cardShadow()
    }
}

extension View {
    func glassCard() -> some View {
        self.modifier(GlassCardStyle())
    }
}

// MARK: - Global Background Style
struct GlobalBackgroundStyle: ViewModifier {
    func body(content: Content) -> some View {
        ZStack {
            // 固定的全局背景渐变，不受视图切换影响
            Color.globalBackgroundGradient
                .ignoresSafeArea(.all)
                .zIndex(0)
            
            content
        }
        .preferredColorScheme(.dark)
    }
}

// MARK: - Stable Background Style (稳定的背景样式)
struct StableBackgroundStyle: ViewModifier {
    func body(content: Content) -> some View {
        content
            .background(
                Rectangle()
                    .fill(Color.globalBackgroundGradient)
                    .ignoresSafeArea(.all)
                    .drawingGroup() // 优化渲染性能，减少闪烁
            )
    }
}

extension View {
    /// 应用全局背景渐变
    func globalBackground() -> some View {
        self.modifier(GlobalBackgroundStyle())
    }
    
    /// 应用稳定的背景（防止闪烁）
    func stableBackground() -> some View {
        self.modifier(StableBackgroundStyle())
    }
}

// MARK: - Gradient Text Style
struct GradientTextStyle: ViewModifier {
    let gradient: LinearGradient
    
    func body(content: Content) -> some View {
        content
            .overlay(
                gradient
                    .mask(content)
            )
    }
}

extension View {
    func gradientText(_ gradient: LinearGradient = Color.primaryGradient) -> some View {
        self.modifier(GradientTextStyle(gradient: gradient))
    }
}
